% 跟踪精度对比图 - MATLAB版本
% 数据
methods = {'传统方法', '智能框架'};
accuracy = [0.150, 0.070];
improvement = [0, 53.3];

% 创建图形
figure('Position', [100, 100, 800, 600]);

% 子图1：跟踪精度对比
subplot(2,1,1);
bar(accuracy, 'FaceColor', [0.2, 0.6, 0.8]);
set(gca, 'XTickLabel', methods);
ylabel('跟踪精度 (mm)');
title('跟踪精度对比', 'FontSize', 16, 'FontWeight', 'bold');
grid on;

% 添加数值标签
for i = 1:length(accuracy)
    text(i, accuracy(i) + 0.005, sprintf('%.3f', accuracy(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

% 子图2：改进百分比
subplot(2,1,2);
bar(improvement, 'FaceColor', [0.8, 0.4, 0.2]);
set(gca, 'XTickLabel', methods);
ylabel('改进百分比 (%)');
title('性能提升对比', 'FontSize', 16, 'FontWeight', 'bold');
grid on;

% 添加数值标签
for i = 1:length(improvement)
    text(i, improvement(i) + 2, sprintf('%.1f%%', improvement(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

% 保存图片
print('跟踪精度对比图', '-dpng', '-r300');
print('跟踪精度对比图', '-depsc', '-r300');

fprintf('图表已创建完成！\n');
fprintf('传统方法跟踪精度: %.3f mm\n', accuracy(1));
fprintf('智能框架跟踪精度: %.3f mm\n', accuracy(2));
fprintf('性能提升: %.1f%%\n', improvement(2));


































