#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于参考图片风格生成完整的感知-决策-执行-学习控制回路技术路线图
参考图片风格：层次化、模块化、带箭头流程的技术路线图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, Circle, FancyArrowPatch, Rectangle
import numpy as np
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def create_complete_control_loop_roadmap():
    """创建完整的感知-决策-执行-学习控制回路技术路线图"""
    
    # 创建画布
    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 16)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 定义颜色方案（参考图片风格）
    colors = {
        'perception': '#E8F5E8',      # 浅绿色 - 感知层
        'decision': '#E8F0FF',        # 浅蓝色 - 决策层  
        'execution': '#FFF0E8',       # 浅橙色 - 执行层
        'learning': '#F0E8FF',        # 浅紫色 - 学习层
        'arrow': '#4A90E2',           # 蓝色箭头
        'border': '#2E3A59',          # 深色边框
        'core': '#FFE8E8',            # 浅红色 - 核心
        'text': '#2E3A59'             # 深色文字
    }
    
    # 主标题
    ax.text(10, 15.2, '6DOF机器人智能书写系统：完整感知-决策-执行-学习控制回路技术路线图', 
            fontsize=18, fontweight='bold', ha='center', va='center', color=colors['text'])
    
    # =================== 左侧：研究框架 ===================
    
    # 研究背景框
    bg_box = FancyBboxPatch((0.5, 12.5), 3.5, 2, 
                           boxstyle="round,pad=0.1", 
                           facecolor='#F0F8FF', alpha=0.8,
                           edgecolor=colors['border'], linewidth=2)
    ax.add_patch(bg_box)
    ax.text(2.25, 13.5, '研究背景', fontsize=14, fontweight='bold', ha='center', va='center')
    
    bg_items = [
        '• 高精度书写需求',
        '• 复杂轨迹跟踪挑战', 
        '• 实时控制要求',
        '• 自适应学习需求'
    ]
    for i, item in enumerate(bg_items):
        ax.text(0.8, 13.8 - i*0.25, item, fontsize=10, ha='left', va='center')
    
    # 问题分析框
    prob_box = FancyBboxPatch((0.5, 9.5), 3.5, 2.5, 
                             boxstyle="round,pad=0.1", 
                             facecolor='#FFF8DC', alpha=0.8,
                             edgecolor=colors['border'], linewidth=2)
    ax.add_patch(prob_box)
    ax.text(2.25, 11.5, '问题分析', fontsize=14, fontweight='bold', ha='center', va='center')
    
    prob_items = [
        '• 传感器数据融合',
        '• 多目标优化决策',
        '• 预测误差补偿',
        '• 在线学习适应',
        '• 实时性能保证'
    ]
    for i, item in enumerate(prob_items):
        ax.text(0.8, 11.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # 解决问题框
    sol_box = FancyBboxPatch((0.5, 6.5), 3.5, 2.5, 
                            boxstyle="round,pad=0.1", 
                            facecolor='#F0FFF0', alpha=0.8,
                            edgecolor=colors['border'], linewidth=2)
    ax.add_patch(sol_box)
    ax.text(2.25, 8.5, '解决问题', fontsize=14, fontweight='bold', ha='center', va='center')
    
    sol_items = [
        '• 四层智能架构',
        '• 多传感器融合感知',
        '• 预测决策优化',
        '• 智能执行控制',
        '• 自适应学习机制'
    ]
    for i, item in enumerate(sol_items):
        ax.text(0.8, 8.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # 基础理论框
    theory_box = FancyBboxPatch((0.5, 3.5), 3.5, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor='#FFF0F5', alpha=0.8,
                               edgecolor=colors['border'], linewidth=2)
    ax.add_patch(theory_box)
    ax.text(2.25, 5.5, '基础理论', fontsize=14, fontweight='bold', ha='center', va='center')
    
    theory_items = [
        '• B样条轨迹理论',
        '• 多目标优化理论',
        '• 预测控制理论',
        '• 机器学习理论',
        '• 鲁棒控制理论'
    ]
    for i, item in enumerate(theory_items):
        ax.text(0.8, 5.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # =================== 中间：核心控制回路 ===================
    
    # 主框架虚线边界
    main_frame = Rectangle((5, 2), 10, 12, linewidth=3, edgecolor=colors['border'], 
                          facecolor='none', linestyle='--', alpha=0.7)
    ax.add_patch(main_frame)
    
    # 中心标题
    ax.text(10, 13.5, '四层智能集成控制架构', fontsize=16, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # ============ 第4层：智能学习层 ============
    learning_box = FancyBboxPatch((5.5, 11.5), 9, 1.8, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['learning'], alpha=0.7,
                                 edgecolor=colors['border'], linewidth=2)
    ax.add_patch(learning_box)
    ax.text(10, 12.7, '第4层：智能学习层', fontsize=14, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # 学习层子模块
    learning_modules = [
        ('性能评估', 6, 12.2),
        ('在线学习', 8, 12.2), 
        ('经验积累', 12, 12.2),
        ('参数优化', 14, 12.2)
    ]
    
    for name, x, y in learning_modules:
        module_box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor='white', alpha=0.9,
                                   edgecolor=colors['border'], linewidth=1)
        ax.add_patch(module_box)
        ax.text(x, y, name, fontsize=10, fontweight='bold', ha='center', va='center')
    
    # ============ 第3层：智能执行层 ============
    execution_box = FancyBboxPatch((5.5, 9), 9, 1.8, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['execution'], alpha=0.7,
                                  edgecolor=colors['border'], linewidth=2)
    ax.add_patch(execution_box)
    ax.text(10, 10.2, '第3层：智能执行层', fontsize=14, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # 执行层子模块
    execution_modules = [
        ('前馈控制', 6, 9.7),
        ('反馈校正', 8, 9.7),
        ('预测补偿', 12, 9.7),
        ('智能融合', 14, 9.7)
    ]
    
    for name, x, y in execution_modules:
        module_box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor='white', alpha=0.9,
                                   edgecolor=colors['border'], linewidth=1)
        ax.add_patch(module_box)
        ax.text(x, y, name, fontsize=10, fontweight='bold', ha='center', va='center')
    
    # ============ 第2层：智能决策层 ============
    decision_box = FancyBboxPatch((5.5, 6.5), 9, 1.8, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['decision'], alpha=0.7,
                                 edgecolor=colors['border'], linewidth=2)
    ax.add_patch(decision_box)
    ax.text(10, 7.7, '第2层：智能决策层', fontsize=14, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # 决策层子模块
    decision_modules = [
        ('误差预测', 6, 7.2),
        ('策略选择', 8, 7.2),
        ('权重调整', 12, 7.2),
        ('约束处理', 14, 7.2)
    ]
    
    for name, x, y in decision_modules:
        module_box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor='white', alpha=0.9,
                                   edgecolor=colors['border'], linewidth=1)
        ax.add_patch(module_box)
        ax.text(x, y, name, fontsize=10, fontweight='bold', ha='center', va='center')
    
    # ============ 第1层：智能感知层 ============
    perception_box = FancyBboxPatch((5.5, 4), 9, 1.8, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['perception'], alpha=0.7,
                                   edgecolor=colors['border'], linewidth=2)
    ax.add_patch(perception_box)
    ax.text(10, 5.2, '第1层：智能感知层', fontsize=14, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # 感知层子模块
    perception_modules = [
        ('多传感器\n数据融合', 6, 4.7),
        ('状态估计', 8, 4.7),
        ('异常检测', 12, 4.7),
        ('环境感知', 14, 4.7)
    ]
    
    for name, x, y in perception_modules:
        module_box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor='white', alpha=0.9,
                                   edgecolor=colors['border'], linewidth=1)
        ax.add_patch(module_box)
        ax.text(x, y, name, fontsize=9, fontweight='bold', ha='center', va='center')
    
    # ============ 控制回路核心 ============
    core_circle = Circle((10, 2.8), 0.8, facecolor=colors['core'], alpha=0.8,
                        edgecolor=colors['border'], linewidth=3)
    ax.add_patch(core_circle)
    ax.text(10, 2.8, '控制回路\n核心', fontsize=12, fontweight='bold', 
            ha='center', va='center', color=colors['text'])
    
    # =================== 层间连接箭头 ===================
    
    # 主要数据流箭头（向下）
    arrow_positions = [
        (10, 11.5, 10, 10.8),  # 学习层 → 执行层
        (10, 9, 10, 8.3),      # 执行层 → 决策层  
        (10, 6.5, 10, 5.8),    # 决策层 → 感知层
        (10, 4, 10, 3.6),      # 感知层 → 核心
    ]
    
    for x1, y1, x2, y2 in arrow_positions:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                               arrowstyle='->', mutation_scale=25,
                               color=colors['arrow'], linewidth=3, alpha=0.8)
        ax.add_artist(arrow)
    
    # 反馈箭头（向上）
    feedback_arrows = [
        (9.2, 3.6, 9.2, 4),     # 核心 → 感知层
        (9.2, 5.8, 9.2, 6.5),   # 感知层 → 决策层
        (9.2, 8.3, 9.2, 9),     # 决策层 → 执行层
        (9.2, 10.8, 9.2, 11.5), # 执行层 → 学习层
    ]
    
    for x1, y1, x2, y2 in feedback_arrows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                               arrowstyle='->', mutation_scale=20,
                               color='#E74C3C', linewidth=2, alpha=0.7)
        ax.add_artist(arrow)
    
    # =================== 右侧：技术特征与应用 ===================
    
    # 技术优势框
    adv_box = FancyBboxPatch((16, 12.5), 3.5, 2, 
                            boxstyle="round,pad=0.1", 
                            facecolor='#E8F8F5', alpha=0.8,
                            edgecolor=colors['border'], linewidth=2)
    ax.add_patch(adv_box)
    ax.text(17.75, 13.5, '技术优势', fontsize=14, fontweight='bold', ha='center', va='center')
    
    adv_items = [
        '• 53%精度提升',
        '• 29%响应时间减少',
        '• 22%能效改善',
        '• 实时自适应能力'
    ]
    for i, item in enumerate(adv_items):
        ax.text(16.3, 13.8 - i*0.25, item, fontsize=10, ha='left', va='center')
    
    # 应用场景框
    app_box = FancyBboxPatch((16, 9.5), 3.5, 2.5, 
                            boxstyle="round,pad=0.1", 
                            facecolor='#FDF2E9', alpha=0.8,
                            edgecolor=colors['border'], linewidth=2)
    ax.add_patch(app_box)
    ax.text(17.75, 11.5, '应用场景', fontsize=14, fontweight='bold', ha='center', va='center')
    
    app_items = [
        '• 智能制造',
        '• 精密装配',
        '• 艺术创作',
        '• 医疗手术',
        '• 教育培训'
    ]
    for i, item in enumerate(app_items):
        ax.text(16.3, 11.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # 技术指标框
    metric_box = FancyBboxPatch((16, 6.5), 3.5, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor='#EBF5FB', alpha=0.8,
                               edgecolor=colors['border'], linewidth=2)
    ax.add_patch(metric_box)
    ax.text(17.75, 8.5, '关键指标', fontsize=14, fontweight='bold', ha='center', va='center')
    
    metric_items = [
        '• 跟踪精度: ±0.1mm',
        '• 控制频率: 1kHz',
        '• 系统延迟: <3ms',
        '• 学习收敛: <100次',
        '• 鲁棒性: ±30%扰动'
    ]
    for i, item in enumerate(metric_items):
        ax.text(16.3, 8.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # 发展方向框
    future_box = FancyBboxPatch((16, 3.5), 3.5, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor='#F4ECF7', alpha=0.8,
                               edgecolor=colors['border'], linewidth=2)
    ax.add_patch(future_box)
    ax.text(17.75, 5.5, '发展方向', fontsize=14, fontweight='bold', ha='center', va='center')
    
    future_items = [
        '• 深度学习融合',
        '• 云边协同控制',
        '• 数字孪生技术',
        '• 群体智能协作',
        '• 人机协同优化'
    ]
    for i, item in enumerate(future_items):
        ax.text(16.3, 5.8 - i*0.3, item, fontsize=10, ha='left', va='center')
    
    # =================== 连接主框架的箭头 ===================
    
    # 左侧到中心的箭头
    left_arrows = [
        (4, 13.5, 5.5, 12.4),  # 研究背景 → 学习层
        (4, 10.75, 5.5, 9.9),  # 问题分析 → 执行层
        (4, 7.75, 5.5, 7.4),   # 解决问题 → 决策层
        (4, 4.75, 5.5, 4.9),   # 基础理论 → 感知层
    ]
    
    for x1, y1, x2, y2 in left_arrows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                               arrowstyle='->', mutation_scale=20,
                               color=colors['arrow'], linewidth=2, alpha=0.6)
        ax.add_artist(arrow)
    
    # 中心到右侧的箭头
    right_arrows = [
        (14.5, 12.4, 16, 13.5),  # 学习层 → 技术优势
        (14.5, 9.9, 16, 10.75),  # 执行层 → 应用场景
        (14.5, 7.4, 16, 7.75),   # 决策层 → 技术指标
        (14.5, 4.9, 16, 4.75),   # 感知层 → 发展方向
    ]
    
    for x1, y1, x2, y2 in right_arrows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                               arrowstyle='->', mutation_scale=20,
                               color=colors['arrow'], linewidth=2, alpha=0.6)
        ax.add_artist(arrow)
    
    # =================== 底部说明 ===================
    
    # 数据流说明
    ax.text(10, 1.2, '数据流向说明：', fontsize=12, fontweight='bold', ha='center', va='center')
    ax.text(10, 0.8, '→ 前向数据流（蓝色）   ← 反馈数据流（红色）   ⟷ 双向通信', 
            fontsize=10, ha='center', va='center', style='italic')
    
    # 性能参数说明
    ax.text(10, 0.4, '关键性能：1kHz实时控制 | ±0.1mm精度 | <3ms延迟 | 53%性能提升', 
            fontsize=11, fontweight='bold', ha='center', va='center', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='#FFFACD', alpha=0.8))
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在生成完整的感知-决策-执行-学习控制回路技术路线图...")
    
    # 生成技术路线图
    fig = create_complete_control_loop_roadmap()
    
    # 保存图片
    output_file = 'Complete_Control_Loop_Roadmap.png'
    fig.savefig(output_file, dpi=400, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    # 同时保存PDF版本
    pdf_file = 'Complete_Control_Loop_Roadmap.pdf'
    fig.savefig(pdf_file, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    plt.show()
    
    print(f"\n✅ 完整控制回路技术路线图已成功生成:")
    print(f"📁 PNG格式: {output_file}")
    print(f"📁 PDF格式: {pdf_file}")
    print(f"📊 图像分辨率: 400 DPI (超高清)")
    print(f"📐 图像尺寸: 20x16 英寸")
    print(f"\n🎯 技术路线图特色:")
    print(f"   • 参考您提供图片的层次化设计风格")
    print(f"   • 完整展示四层智能控制架构")
    print(f"   • 清晰的数据流向和反馈机制")
    print(f"   • 详细的技术模块和性能指标")
    print(f"   • 专业的学术发表质量")

if __name__ == "__main__":
    main()