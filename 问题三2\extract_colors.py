#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取技术路线图中的RGB颜色值
"""

import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
import pandas as pd

def extract_rgb_colors():
    """提取技术路线图中使用的主要RGB颜色值"""
    
    print("=== 技术路线图RGB颜色提取分析 ===\n")
    
    # 定义在代码中使用的颜色
    colors = {
        '感知层 (Perception)': {
            'hex': '#1976D2', 
            'rgb': (25, 118, 210),
            'description': '深蓝色 - 代表稳定的数据感知和信息获取'
        },
        '决策层 (Decision)': {
            'hex': '#388E3C', 
            'rgb': (56, 142, 60),
            'description': '深绿色 - 代表智能决策和策略制定'
        },
        '执行层 (Execution)': {
            'hex': '#F57C00', 
            'rgb': (245, 124, 0),
            'description': '橙色 - 代表动态执行和实时控制'
        },
        '学习层 (Learning)': {
            'hex': '#7B1FA2', 
            'rgb': (123, 31, 162),
            'description': '紫色 - 代表机器学习和自适应优化'
        },
        '中心核心 (Core)': {
            'hex': '#1976D2', 
            'rgb': (25, 118, 210),
            'description': '主蓝色 - 智能控制核心'
        },
        '性能指标框 (Metrics)': {
            'hex': '#FFC107', 
            'rgb': (255, 193, 7),
            'description': '金黄色 - 关键性能指标展示'
        },
        '技术特征框 (Features)': {
            'hex': '#E91E63', 
            'rgb': (233, 30, 99),
            'description': '粉红色 - 技术特征和优势'
        },
        '连接箭头 (Arrows)': {
            'hex': '#424242', 
            'rgb': (66, 66, 66),
            'description': '深灰色 - 系统连接和数据流'
        }
    }
    
    # 显示颜色分析结果
    print("📊 主要颜色RGB分析:")
    print("=" * 80)
    
    for name, info in colors.items():
        r, g, b = info['rgb']
        print(f"🎨 {name}:")
        print(f"   RGB值: ({r}, {g}, {b})")
        print(f"   Hex值: {info['hex']}")
        print(f"   描述: {info['description']}")
        print(f"   CSS格式: rgb({r}, {g}, {b})")
        print(f"   归一化: ({r/255:.3f}, {g/255:.3f}, {b/255:.3f})")
        print("-" * 60)
    
    # 创建颜色调色板可视化
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 第一行：主要颜色展示
    color_names = []
    color_values = []
    color_hexs = []
    
    for name, info in colors.items():
        color_names.append(name.split(' (')[0])  # 只取中文部分
        color_values.append([c/255.0 for c in info['rgb']])  # 归一化RGB
        color_hexs.append(info['hex'])
    
    # 绘制颜色条
    y_pos = np.arange(len(color_names))
    bars = ax1.barh(y_pos, [1]*len(color_names), color=color_hexs, alpha=0.8, height=0.6)
    
    ax1.set_yticks(y_pos)
    ax1.set_yticklabels(color_names, fontsize=11, fontweight='bold')
    ax1.set_xlabel('颜色展示', fontsize=12, fontweight='bold')
    ax1.set_title('6DOF机器人智能控制系统 - 技术路线图颜色调色板', fontsize=14, fontweight='bold', pad=20)
    ax1.set_xlim(0, 1)
    
    # 在每个颜色条上添加RGB值
    for i, (bar, info) in enumerate(zip(bars, colors.values())):
        r, g, b = info['rgb']
        ax1.text(0.5, i, f'RGB({r},{g},{b})', ha='center', va='center', 
                fontsize=10, fontweight='bold', color='white' if sum(info['rgb']) < 400 else 'black')
    
    # 第二行：颜色分布分析
    rgb_data = np.array([list(info['rgb']) for info in colors.values()])
    x_pos = np.arange(3)
    
    # 分别绘制R、G、B的分布
    ax2.bar(x_pos - 0.25, rgb_data.mean(axis=0), 0.5, 
           label='平均值', alpha=0.7, color=['#FF5722', '#4CAF50', '#2196F3'])
    ax2.bar(x_pos + 0.25, rgb_data.max(axis=0), 0.5, 
           label='最大值', alpha=0.7, color=['#D32F2F', '#388E3C', '#1976D2'])
    
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(['Red (R)', 'Green (G)', 'Blue (B)'], fontweight='bold')
    ax2.set_ylabel('RGB数值 (0-255)', fontweight='bold')
    ax2.set_title('RGB通道分布统计', fontsize=12, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i in range(3):
        ax2.text(i-0.25, rgb_data.mean(axis=0)[i] + 5, f'{rgb_data.mean(axis=0)[i]:.0f}', 
                ha='center', fontweight='bold')
        ax2.text(i+0.25, rgb_data.max(axis=0)[i] + 5, f'{rgb_data.max(axis=0)[i]:.0f}', 
                ha='center', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('RGB_Color_Analysis.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('RGB_Color_Analysis.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    # 保存详细的颜色数据到CSV
    df_colors = pd.DataFrame([
        {
            '颜色名称': name,
            'Hex值': info['hex'],
            'R值': info['rgb'][0],
            'G值': info['rgb'][1],
            'B值': info['rgb'][2],
            '描述': info['description']
        }
        for name, info in colors.items()
    ])
    
    df_colors.to_csv('技术路线图颜色数据.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 颜色分析完成!")
    print(f"📁 RGB分析图表已保存: RGB_Color_Analysis.png / RGB_Color_Analysis.pdf")
    print(f"📊 详细颜色数据已保存: 技术路线图颜色数据.csv")
    
    # 颜色统计摘要
    print(f"\n📈 颜色统计摘要:")
    print(f"   总计使用颜色: {len(colors)} 种")
    print(f"   RGB平均值: R={rgb_data.mean(axis=0)[0]:.0f}, G={rgb_data.mean(axis=0)[1]:.0f}, B={rgb_data.mean(axis=0)[2]:.0f}")
    print(f"   色彩丰富度: {np.std(rgb_data):.1f} (标准差)")
    
    return colors

if __name__ == "__main__":
    extract_rgb_colors()