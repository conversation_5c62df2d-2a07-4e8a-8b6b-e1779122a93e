#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6-DOF机器人智能书写系统技术路线图生成器
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def create_tech_roadmap():
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 14)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'flow': '#2E8B57',       # 深海绿 - 流程箭头
        'theory': '#4169E1',     # 皇家蓝 - 理论基础
        'algorithm': '#FF8C00',  # 深橙色 - 算法设计
        'system': '#32CD32',     # 酸橙绿 - 系统集成
        'application': '#9370DB', # 中紫色 - 应用层
        'tools': '#708090',      # 石板灰 - 工具
        'results': '#FFD700'     # 金色 - 成果
    }
    
    # 主标题
    title_box = FancyBboxPatch((1, 13), 10, 0.8, 
                              boxstyle="round,pad=0.02", 
                              facecolor='lightblue', alpha=0.3,
                              edgecolor='navy', linewidth=2)
    ax.add_patch(title_box)
    ax.text(6, 13.4, '6-DOF机器人智能书写系统技术路线图', 
            fontsize=20, fontweight='bold', ha='center', va='center')
    
    # 左侧研究流程
    flow_steps = [
        '研究框架', '问题分析', '算法设计', '解决方案', 
        '技术集成', '性能验证', '应用推广', '工业应用', '效果评价'
    ]
    
    flow_positions = []
    for i, step in enumerate(flow_steps):
        y = 12 - i * 1.3
        flow_positions.append((0.8, y))
        
        # 流程框
        rect = FancyBboxPatch((0.2, y-0.2), 1.2, 0.4, 
                              boxstyle="round,pad=0.02", 
                              facecolor=colors['flow'], alpha=0.8)
        ax.add_patch(rect)
        ax.text(0.8, y, step, ha='center', va='center', 
                fontsize=11, color='white', fontweight='bold')
        
        # 向下箭头
        if i < len(flow_steps) - 1:
            ax.arrow(0.8, y-0.25, 0, -0.8, head_width=0.08, head_length=0.12, 
                    fc=colors['flow'], ec=colors['flow'], alpha=0.8, linewidth=2)
    
    # =============== 第一层：智能控制理论基础 ===============
    theory_box = FancyBboxPatch((2.2, 11), 7, 1.8, 
                               boxstyle="round,pad=0.05", 
                               facecolor=colors['theory'], alpha=0.15,
                               edgecolor=colors['theory'], linestyle='--', linewidth=2)
    ax.add_patch(theory_box)
    ax.text(5.7, 12.5, '智能控制理论基础', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['theory'])
    
    # 理论基础模块
    theory_modules = [
        ('多目标优化\n•Pareto理论\n•NSGA-II算法\n•约束优化', 3, 11.8),
        ('预测控制理论\n•状态空间建模\n•预测时域控制\n•MPC优化', 5.7, 11.8),
        ('学习控制理论\n•递归最小二乘\n•神经网络补偿\n•强化学习', 8.4, 11.8)
    ]
    
    for text, x, y in theory_modules:
        box = FancyBboxPatch((x-0.8, y-0.5), 1.6, 1, 
                            boxstyle="round,pad=0.03", 
                            facecolor=colors['theory'], alpha=0.3,
                            edgecolor=colors['theory'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第二层：核心技术创新与算法设计 ===============
    algo_box = FancyBboxPatch((2.2, 8.2), 7, 2.5, 
                             boxstyle="round,pad=0.05", 
                             facecolor=colors['algorithm'], alpha=0.15,
                             edgecolor=colors['algorithm'], linestyle='--', linewidth=2)
    ax.add_patch(algo_box)
    ax.text(5.7, 10.4, '核心技术创新与算法设计', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['algorithm'])
    
    # 四层智能架构
    layers = [
        ('智能感知层\n•多传感器融合\n•状态估计\n•异常检测\n•环境感知', 3, 9.8),
        ('智能决策层\n•误差预测\n•策略选择\n•权重自适应\n•约束处理', 4.8, 9.8),
        ('智能执行层\n•轨迹优化\n•实时控制\n•补偿校正\n•安全监控', 6.6, 9.8),
        ('智能学习层\n•在线学习\n•参数辨识\n•经验积累\n•性能优化', 4.8, 8.8)
    ]
    
    for text, x, y in layers:
        box = FancyBboxPatch((x-0.7, y-0.4), 1.4, 0.8, 
                            boxstyle="round,pad=0.03", 
                            facecolor=colors['algorithm'], alpha=0.4,
                            edgecolor=colors['algorithm'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=8, fontweight='bold')
    
    # 系统集成架构
    integration_box = FancyBboxPatch((8.4-0.7, 9.3-0.5), 1.4, 1, 
                                    boxstyle="round,pad=0.03", 
                                    facecolor=colors['system'], alpha=0.4,
                                    edgecolor=colors['system'], linewidth=1)
    ax.add_patch(integration_box)
    ax.text(8.4, 9.3, '系统集成架构\n•四层协同控制\n•层间通信协议\n•实时调度\n•故障处理', 
            ha='center', va='center', fontsize=8, fontweight='bold')
    
    # =============== 第三层：产业化应用与推广 ===============
    app_box = FancyBboxPatch((2.2, 5), 7, 2.8, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['application'], alpha=0.15,
                            edgecolor=colors['application'], linestyle='--', linewidth=2)
    ax.add_patch(app_box)
    ax.text(5.7, 7.5, '产业化应用与推广', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['application'])
    
    # 应用模块
    app_modules = [
        ('应用领域拓展\n•精密制造\n•自动化教育\n•艺术创作\n•医疗康复', 3.2, 6.8),
        ('性能标准制定\n•跟踪精度<0.07mm\n•响应速度<10ms\n•能效>85%\n•鲁棒性>0.85', 6.2, 6.8),
        ('技术转移路径\n•专利保护\n•技术许可\n•产业化合作\n•标准制定', 3.2, 5.8),
        ('市场推广策略\n•行业标准\n•技术验证\n•合作伙伴\n•市场推广', 6.2, 5.8)
    ]
    
    for text, x, y in app_modules:
        box = FancyBboxPatch((x-0.8, y-0.5), 1.6, 1, 
                            boxstyle="round,pad=0.03", 
                            facecolor=colors['application'], alpha=0.4,
                            edgecolor=colors['application'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=8, fontweight='bold')
    
    # =============== 右侧工具与平台 ===============
    tools_data = [
        ('理论验证与\n数学建模工具\n\n•MATLAB/Simulink\n•数学证明工具\n•仿真平台\n•理论分析', 10.5, 11.8),
        ('算法实现与\n优化工具\n\n•C++/Python\n•ROS实时框架\n•优化算法库\n•并行计算', 10.5, 9.8),
        ('硬件平台与\n实验验证\n\n•KUKA LBR iiwa\n•6-DOF机器人\n•传感器系统\n•实时测试', 10.5, 7.8),
        ('学术发表与\n成果转化\n\n•IEEE期刊论文\n•实验数据分析\n•统计显著性\n•影响因子评估', 10.5, 5.8)
    ]
    
    for text, x, y in tools_data:
        box = FancyBboxPatch((x-1, y-0.7), 2, 1.4, 
                            boxstyle="round,pad=0.03", 
                            facecolor=colors['tools'], alpha=0.2,
                            edgecolor=colors['tools'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 底部关键指标与成果 ===============
    results_data = [
        ('驱动因素\n\n工业4.0需求驱动\n精密制造技术要求\n人工智能技术发展\n智能制造升级', 2.2, 3.5),
        ('核心创新\n\n四层智能架构\n多目标B样条优化\n预测误差补偿\n实时性能保障', 4.5, 3.5),
        ('关键技术\n\nB样条轨迹优化\n预测控制补偿\n自适应学习机制\n智能融合策略', 6.8, 3.5),
        ('预期成果\n\n跟踪精度提升53%\n响应时间减少29%\n能效改善22%\n鲁棒性提升50%', 9.1, 3.5)
    ]
    
    for text, x, y in results_data:
        box = FancyBboxPatch((x-1, y-1), 2, 2, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['results'], alpha=0.3,
                            edgecolor=colors['results'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
    
    # =============== 连接箭头 ===============
    # 从流程到各技术层的连接
    connections = [
        # 研究框架 -> 理论基础
        (1.4, 12, 2.2, 11.8, colors['theory']),
        # 算法设计 -> 核心技术
        (1.4, 9.1, 2.2, 9.3, colors['algorithm']),
        # 应用推广 -> 产业化
        (1.4, 6.5, 2.2, 6.4, colors['application']),
    ]
    
    for x1, y1, x2, y2, color in connections:
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc=color, ec=color, alpha=0.7, linewidth=2)
        ax.add_artist(arrow)
    
    # 从技术层到工具的连接
    tool_connections = [
        (9.2, 11.8, 9.5, 11.8, colors['tools']),
        (9.2, 9.3, 9.5, 9.8, colors['tools']),
        (9.2, 6.4, 9.5, 7.8, colors['tools']),
        (8.4, 5.8, 9.5, 5.8, colors['tools'])
    ]
    
    for x1, y1, x2, y2, color in tool_connections:
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=15, fc=color, ec=color, alpha=0.6, linewidth=1.5)
        ax.add_artist(arrow)
    
    # 内部层次连接（四层架构）
    layer_connections = [
        # 感知层 -> 决策层
        (3.7, 9.8, 4.1, 9.8),
        # 决策层 -> 执行层  
        (5.5, 9.8, 5.9, 9.8),
        # 执行层 -> 学习层
        (6.6, 9.4, 5.5, 8.8),
        # 学习层 -> 感知层 (反馈)
        (4.1, 8.8, 3.7, 9.4),
        # 集成架构连接
        (7.7, 9.3, 8.4, 9.3)
    ]
    
    for x1, y1, x2, y2 in layer_connections:
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                               arrowstyle="->", shrinkA=3, shrinkB=3,
                               mutation_scale=10, fc='darkred', ec='darkred', alpha=0.7, linewidth=1)
        ax.add_artist(arrow)
    
    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=colors['theory'], 
                   markersize=12, label='理论基础层'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=colors['algorithm'], 
                   markersize=12, label='算法设计层'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=colors['application'], 
                   markersize=12, label='应用推广层'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=colors['tools'], 
                   markersize=12, label='工具与平台'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=colors['results'], 
                   markersize=12, label='成果与指标')
    ]
    
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 1), fontsize=10)
    
    # 添加版权信息
    ax.text(11.5, 0.5, '© 2025 6-DOF机器人智能书写系统', 
            fontsize=8, ha='right', va='bottom', alpha=0.7, style='italic')
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在生成6-DOF机器人智能书写系统技术路线图...")
    
    # 生成图表
    fig = create_tech_roadmap()
    
    # 保存为高清PNG
    output_file = "6DOF_Robot_Intelligent_Writing_System_Tech_Roadmap.png"
    fig.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none', 
                pad_inches=0.2, format='png')
    
    print(f"技术路线图已成功保存为: {output_file}")
    print("图像分辨率: 300 DPI")
    print("图像格式: PNG")
    
    # 显示图表
    plt.show()
    
    return output_file

if __name__ == "__main__":
    main()