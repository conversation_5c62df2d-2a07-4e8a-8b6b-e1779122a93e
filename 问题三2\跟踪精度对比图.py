#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跟踪精度对比图 - Python版本
使用matplotlib创建专业图表
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 数据
methods = ['传统方法', '智能框架']
accuracy = [0.150, 0.070]
improvement = [0, 53.3]

# 创建图形
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
fig.suptitle('跟踪精度对比分析', fontsize=18, fontweight='bold')

# 子图1：跟踪精度对比
bars1 = ax1.bar(methods, accuracy, color=['#3498db', '#e74c3c'], alpha=0.8)
ax1.set_ylabel('跟踪精度 (mm)', fontsize=12)
ax1.set_title('跟踪精度对比', fontsize=14, fontweight='bold')
ax1.grid(True, alpha=0.3)

# 添加数值标签
for bar, acc in zip(bars1, accuracy):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
             f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

# 子图2：改进百分比
bars2 = ax2.bar(methods, improvement, color=['#f39c12', '#27ae60'], alpha=0.8)
ax2.set_ylabel('改进百分比 (%)', fontsize=12)
ax2.set_title('性能提升对比', fontsize=14, fontweight='bold')
ax2.grid(True, alpha=0.3)

# 添加数值标签
for bar, imp in zip(bars2, improvement):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
             f'{imp:.1f}%', ha='center', va='bottom', fontweight='bold')

# 调整布局
plt.tight_layout()

# 保存图片
plt.savefig('跟踪精度对比图.png', dpi=300, bbox_inches='tight')
plt.savefig('跟踪精度对比图.pdf', bbox_inches='tight')

# 显示图表
plt.show()

print("图表创建完成！")
print(f"传统方法跟踪精度: {accuracy[0]:.3f} mm")
print(f"智能框架跟踪精度: {accuracy[1]:.3f} mm")
print(f"性能提升: {improvement[1]:.1f}%")

































