#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6-DOF机器人智能书写系统技术路线图生成器 - 改进版
解决字体和显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
from matplotlib.lines import Line2D
import numpy as np

# 解决中文字体显示问题
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

def create_enhanced_tech_roadmap():
    """创建增强版技术路线图"""
    # 创建更大的图形
    fig, ax = plt.subplots(1, 1, figsize=(24, 16))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    # 定义专业配色方案
    colors = {
        'primary': '#1f4e79',      # 深蓝 - 主色调
        'secondary': '#2e75b6',    # 中蓝 - 次色调  
        'accent1': '#ff6b35',      # 橙红 - 强调色1
        'accent2': '#f7941d',      # 橙黄 - 强调色2
        'success': '#28a745',      # 成功绿
        'info': '#17a2b8',         # 信息蓝
        'warning': '#ffc107',      # 警告黄
        'light': '#f8f9fa',        # 浅色
        'dark': '#343a40'          # 深色
    }
    
    # 主标题背景
    title_bg = FancyBboxPatch((1, 14.5), 12, 1.2, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['primary'], alpha=0.9,
                             edgecolor=colors['dark'], linewidth=3)
    ax.add_patch(title_bg)
    ax.text(7, 15.1, '6-DOF机器人智能书写系统', 
            fontsize=24, fontweight='bold', ha='center', va='center', color='white')
    ax.text(7, 14.7, '技术路线图与创新框架', 
            fontsize=18, ha='center', va='center', color='white', alpha=0.9)
    
    # =============== 左侧研究流程主线 ===============
    flow_data = [
        ('研究背景\n与需求', '工业4.0与精密制造需求驱动', 13.5),
        ('问题识别\n与分析', '多目标冲突与实时性挑战', 12.3), 
        ('理论创新\n与设计', '智能控制理论框架构建', 11.1),
        ('算法开发\n与优化', '核心算法设计与实现', 9.9),
        ('系统集成\n与验证', '四层架构协同控制', 8.7),
        ('实验验证\n与测试', '硬件平台综合验证', 7.5),
        ('性能评估\n与优化', '多维度性能评估体系', 6.3),
        ('应用推广\n与转化', '产业化应用与推广', 5.1),
        ('成果总结\n与展望', '技术成果与未来发展', 3.9)
    ]
    
    # 绘制主流程线
    for i, (main_text, sub_text, y) in enumerate(flow_data):
        # 主流程框
        main_box = FancyBboxPatch((0.3, y-0.3), 1.8, 0.6, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor=colors['primary'], alpha=0.8,
                                 edgecolor=colors['dark'], linewidth=2)
        ax.add_patch(main_box)
        ax.text(1.2, y, main_text, ha='center', va='center', 
                fontsize=11, color='white', fontweight='bold')
        
        # 说明文字
        ax.text(2.5, y, sub_text, ha='left', va='center', 
                fontsize=10, color=colors['dark'], style='italic')
        
        # 连接箭头
        if i < len(flow_data) - 1:
            ax.arrow(1.2, y-0.35, 0, -0.75, head_width=0.1, head_length=0.15, 
                    fc=colors['primary'], ec=colors['primary'], alpha=0.8, linewidth=3)
    
    # =============== 第一层：智能控制理论基础 ===============
    theory_main = FancyBboxPatch((4.5, 12.5), 8, 2.2, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['secondary'], alpha=0.15,
                                edgecolor=colors['secondary'], linestyle='-', linewidth=3)
    ax.add_patch(theory_main)
    
    # 理论层标题
    ax.text(8.5, 14.2, '第一层：智能控制理论基础', ha='center', va='center', 
            fontsize=16, fontweight='bold', color=colors['secondary'])
    
    # 理论模块
    theory_items = [
        ('多目标优化理论\n\n• Pareto最优理论\n• NSGA-II改进算法\n• 约束处理机制\n• 实时优化策略', 5.5, 13.3),
        ('预测控制理论\n\n• 状态空间建模\n• 预测时域控制\n• 模型预测控制\n• 误差补偿机制', 8.5, 13.3),
        ('智能学习理论\n\n• 递归最小二乘\n• 神经网络补偿\n• 强化学习策略\n• 自适应参数调整', 11.5, 13.3)
    ]
    
    for text, x, y in theory_items:
        box = FancyBboxPatch((x-1.2, y-0.7), 2.4, 1.4, 
                            boxstyle="round,pad=0.08", 
                            facecolor=colors['secondary'], alpha=0.3,
                            edgecolor=colors['secondary'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
    
    # =============== 第二层：四层智能架构设计 ===============
    arch_main = FancyBboxPatch((4.5, 9.5), 8, 2.5, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['accent1'], alpha=0.15,
                              edgecolor=colors['accent1'], linestyle='-', linewidth=3)
    ax.add_patch(arch_main)
    
    ax.text(8.5, 11.7, '第二层：四层智能架构设计', ha='center', va='center', 
            fontsize=16, fontweight='bold', color=colors['accent1'])
    
    # 四层架构
    arch_layers = [
        ('第一层：智能感知\n\n• 多传感器数据融合\n• 扩展卡尔曼滤波\n• 异常检测算法\n• 环境状态感知', 5.5, 11),
        ('第二层：智能决策\n\n• 动态误差预测\n• 策略智能选择\n• 权重自适应调整\n• 约束条件处理', 7.5, 11),
        ('第三层：智能执行\n\n• B样条轨迹优化\n• 实时控制融合\n• 预测误差补偿\n• 安全监控保护', 9.5, 11),
        ('第四层：智能学习\n\n• 在线参数辨识\n• 经验知识积累\n• 性能持续优化\n• 自适应能力提升', 11.5, 11),
        ('系统集成架构\n\n• 四层协同控制\n• 层间通信协议\n• 实时任务调度\n• 故障检测恢复', 8.5, 10)
    ]
    
    for i, (text, x, y) in enumerate(arch_layers):
        if i < 4:  # 前四个是横向排列
            box = FancyBboxPatch((x-0.8, y-0.6), 1.6, 1.2, 
                                boxstyle="round,pad=0.05", 
                                facecolor=colors['accent1'], alpha=0.4,
                                edgecolor=colors['accent1'], linewidth=2)
        else:  # 最后一个是集成架构
            box = FancyBboxPatch((x-1.2, y-0.5), 2.4, 1, 
                                boxstyle="round,pad=0.05", 
                                facecolor=colors['success'], alpha=0.4,
                                edgecolor=colors['success'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第三层：关键技术实现 ===============
    tech_main = FancyBboxPatch((4.5, 6.5), 8, 2.5, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['success'], alpha=0.15,
                              edgecolor=colors['success'], linestyle='-', linewidth=3)
    ax.add_patch(tech_main)
    
    ax.text(8.5, 8.7, '第三层：关键技术实现', ha='center', va='center', 
            fontsize=16, fontweight='bold', color=colors['success'])
    
    # 技术实现模块
    tech_items = [
        ('自适应B样条优化\n\n• 曲率感知密度函数\n• 控制点自适应选择\n• 多目标Pareto优化\n• 实时轨迹生成', 6, 8),
        ('预测误差补偿\n\n• 动态系统建模\n• 多步预测算法\n• 前馈补偿控制\n• 置信度评估', 8.5, 8),
        ('智能融合控制\n\n• 多策略控制融合\n• 置信度加权机制\n• 实时性能优化\n• 鲁棒性保障', 11, 8),
        ('在线学习机制\n\n• 递归参数估计\n• 神经网络补偿\n• 性能评估反馈\n• 持续改进优化', 7.2, 7.2),
        ('实时系统架构\n\n• 1kHz控制频率\n• 多线程并行处理\n• 内存优化管理\n• 硬件加速支持', 9.8, 7.2)
    ]
    
    for text, x, y in tech_items:
        box = FancyBboxPatch((x-1, y-0.5), 2, 1, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['success'], alpha=0.4,
                            edgecolor=colors['success'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第四层：应用验证与推广 ===============
    app_main = FancyBboxPatch((4.5, 3.5), 8, 2.5, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['warning'], alpha=0.15,
                             edgecolor=colors['warning'], linestyle='-', linewidth=3)
    ax.add_patch(app_main)
    
    ax.text(8.5, 5.7, '第四层：应用验证与推广', ha='center', va='center', 
            fontsize=16, fontweight='bold', color=colors['warning'])
    
    # 应用验证模块
    app_items = [
        ('硬件平台验证\n\n• KUKA LBR iiwa 14\n• 6自由度机器人\n• 多传感器系统\n• 实时控制平台', 6, 5),
        ('性能测试评估\n\n• 跟踪精度测试\n• 响应时间评估\n• 能效分析评价\n• 鲁棒性验证', 8.5, 5),
        ('产业应用推广\n\n• 精密制造应用\n• 教育自动化\n• 艺术创作系统\n• 医疗康复设备', 11, 5),
        ('标准制定推广\n\n• 技术标准制定\n• 行业规范建立\n• 专利保护申请\n• 技术转移许可', 7.2, 4.2),
        ('学术成果发表\n\n• IEEE期刊论文\n• 国际会议报告\n• 技术专著出版\n• 学术影响推广', 9.8, 4.2)
    ]
    
    for text, x, y in app_items:
        box = FancyBboxPatch((x-1, y-0.5), 2, 1, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['warning'], alpha=0.4,
                            edgecolor=colors['warning'], linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 右侧成果与指标 ===============
    # 核心创新成果
    innovation_box = FancyBboxPatch((13, 12), 2.8, 3, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['info'], alpha=0.2,
                                   edgecolor=colors['info'], linewidth=3)
    ax.add_patch(innovation_box)
    ax.text(14.4, 14.7, '核心创新成果', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['info'])
    
    innovation_text = """
• 四层智能控制架构
• 多目标B样条优化
• 预测误差补偿框架
• 自适应学习机制
• 实时性能保障体系

理论贡献：
• Lyapunov稳定性证明
• 收敛性分析保证
• 鲁棒性理论框架
• 复杂度分析优化
"""
    ax.text(14.4, 13.2, innovation_text, ha='center', va='center', 
            fontsize=10, color=colors['dark'])
    
    # 性能指标
    performance_box = FancyBboxPatch((13, 8.5), 2.8, 3, 
                                    boxstyle="round,pad=0.1", 
                                    facecolor=colors['accent2'], alpha=0.2,
                                    edgecolor=colors['accent2'], linewidth=3)
    ax.add_patch(performance_box)
    ax.text(14.4, 11.2, '关键性能指标', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['accent2'])
    
    performance_text = """
性能提升指标：

跟踪精度提升：53%
(0.070mm vs 0.150mm)

响应时间减少：29%
(8.5ms vs 12.0ms)

能效改善：22%
(82% vs 105%)

鲁棒性提升：50%
(0.900 vs 0.600)

统计显著性：p<0.001
"""
    ax.text(14.4, 9.7, performance_text, ha='center', va='center', 
            fontsize=10, color=colors['dark'], fontweight='bold')
    
    # 应用前景
    future_box = FancyBboxPatch((13, 5), 2.8, 3, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['success'], alpha=0.2,
                               edgecolor=colors['success'], linewidth=3)
    ax.add_patch(future_box)
    ax.text(14.4, 7.7, '应用前景展望', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['success'])
    
    future_text = """
应用领域：

• 精密制造与装配
• 教育机器人系统  
• 艺术创作与设计
• 医疗康复设备
• 科研实验平台

市场价值：
• 技术标准制定
• 专利技术转移
• 产业化合作
• 国际技术输出
"""
    ax.text(14.4, 6.2, future_text, ha='center', va='center', 
            fontsize=10, color=colors['dark'])
    
    # =============== 连接关系与数据流 ===============
    # 主流程到技术层的连接
    main_connections = [
        (2.1, 13.5, 4.5, 13.5, colors['secondary']),  # 理论创新 -> 理论基础
        (2.1, 11.1, 4.5, 10.8, colors['accent1']),    # 算法开发 -> 架构设计
        (2.1, 8.7, 4.5, 8, colors['success']),        # 系统集成 -> 技术实现
        (2.1, 6.3, 4.5, 5, colors['warning'])         # 应用推广 -> 应用验证
    ]
    
    for x1, y1, x2, y2, color in main_connections:
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=25, fc=color, ec=color, 
                               alpha=0.7, linewidth=3)
        ax.add_artist(arrow)
    
    # 技术层到成果的连接
    result_connections = [
        (12.5, 13.5, 13, 13.5, colors['info']),       # 理论 -> 创新成果
        (12.5, 10.5, 13, 10, colors['accent2']),      # 架构 -> 性能指标  
        (12.5, 5, 13, 6.5, colors['success'])         # 应用 -> 应用前景
    ]
    
    for x1, y1, x2, y2, color in result_connections:
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc=color, ec=color, 
                               alpha=0.6, linewidth=2)
        ax.add_artist(arrow)
    
    # 底部版权和制作信息
    copyright_box = FancyBboxPatch((1, 0.5), 12, 1, 
                                  boxstyle="round,pad=0.05", 
                                  facecolor=colors['light'], alpha=0.5,
                                  edgecolor=colors['dark'], linewidth=1)
    ax.add_patch(copyright_box)
    ax.text(7, 1, '6-DOF机器人智能书写系统技术路线图', 
            fontsize=14, ha='center', va='center', fontweight='bold', color=colors['dark'])
    ax.text(7, 0.7, '© 2025 智能机器人控制技术研究 | 高精度轨迹跟踪与多目标优化', 
            fontsize=10, ha='center', va='center', style='italic', color=colors['dark'], alpha=0.8)
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在生成增强版6-DOF机器人智能书写系统技术路线图...")
    
    # 生成图表
    fig = create_enhanced_tech_roadmap()
    
    # 保存为超高清PNG
    output_file = "6DOF_Robot_Tech_Roadmap_Enhanced.png"
    fig.savefig(output_file, dpi=400, bbox_inches='tight', 
                facecolor='white', edgecolor='none', 
                pad_inches=0.3, format='png')
    
    print(f"增强版技术路线图已成功保存为: {output_file}")
    print("图像分辨率: 400 DPI (超高清)")
    print("图像格式: PNG")
    print("图像尺寸: 24x16 英寸")
    
    # 同时保存PDF版本
    pdf_file = "6DOF_Robot_Tech_Roadmap_Enhanced.pdf"
    fig.savefig(pdf_file, bbox_inches='tight', 
                facecolor='white', edgecolor='none', 
                pad_inches=0.3, format='pdf')
    print(f"PDF版本已保存为: {pdf_file}")
    
    # 显示图表
    plt.show()
    
    return output_file

if __name__ == "__main__":
    main()