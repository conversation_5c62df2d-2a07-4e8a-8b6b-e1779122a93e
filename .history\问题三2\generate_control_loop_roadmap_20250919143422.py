#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
感知-决策-执行-学习控制回路技术路线图生成器
专门展示四层智能控制架构的详细技术路线
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, Circle, FancyArrowPatch, Polygon
from matplotlib.lines import Line2D
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

def create_control_loop_roadmap():
    """创建感知-决策-执行-学习控制回路技术路线图"""
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    # 定义专业配色方案
    colors = {
        'perception': '#4CAF50',    # 绿色 - 感知层
        'decision': '#2196F3',      # 蓝色 - 决策层  
        'execution': '#FF9800',     # 橙色 - 执行层
        'learning': '#9C27B0',      # 紫色 - 学习层
        'flow': '#607D8B',          # 蓝灰色 - 数据流
        'background': '#F5F5F5',    # 浅灰色 - 背景
        'text': '#212121',          # 深灰色 - 文字
        'accent': '#E91E63'         # 粉红色 - 强调
    }
    
    # 主标题
    title_box = FancyBboxPatch((2, 14.5), 16, 1.2, 
                              boxstyle="round,pad=0.1", 
                              facecolor='#1976D2', alpha=0.9,
                              edgecolor='#0D47A1', linewidth=3)
    ax.add_patch(title_box)
    ax.text(10, 15.1, '6-DOF机器人智能书写系统', 
            fontsize=24, fontweight='bold', ha='center', va='center', color='white')
    ax.text(10, 14.7, '感知-决策-执行-学习控制回路技术架构', 
            fontsize=18, ha='center', va='center', color='white', alpha=0.9)
    
    # =============== 控制回路中心圆环 ===============
    # 中心控制回路
    center_x, center_y = 10, 8
    main_radius = 4.5
    
    # 绘制四个层次的扇形区域
    sectors = [
        ('感知层', colors['perception'], 0, 90),      # 右上
        ('决策层', colors['decision'], 90, 180),      # 左上  
        ('执行层', colors['execution'], 180, 270),    # 左下
        ('学习层', colors['learning'], 270, 360)      # 右下
    ]
    
    for layer_name, color, start_angle, end_angle in sectors:
        # 创建扇形
        theta1, theta2 = np.radians(start_angle), np.radians(end_angle)
        
        # 扇形的外圆弧
        angles = np.linspace(theta1, theta2, 50)
        x_outer = center_x + main_radius * np.cos(angles)
        y_outer = center_y + main_radius * np.sin(angles)
        
        # 扇形的内圆弧
        inner_radius = 2.5
        x_inner = center_x + inner_radius * np.cos(angles[::-1])
        y_inner = center_y + inner_radius * np.sin(angles[::-1])
        
        # 连接形成扇形
        x_sector = np.concatenate([x_outer, x_inner])
        y_sector = np.concatenate([y_outer, y_inner])
        
        sector_patch = Polygon(list(zip(x_sector, y_sector)), 
                                  facecolor=color, alpha=0.3, 
                                  edgecolor=color, linewidth=2)
        ax.add_patch(sector_patch)
        
        # 添加层次标签
        mid_angle = np.radians((start_angle + end_angle) / 2)
        label_radius = (main_radius + inner_radius) / 2
        label_x = center_x + label_radius * np.cos(mid_angle)
        label_y = center_y + label_radius * np.sin(mid_angle)
        
        ax.text(label_x, label_y, layer_name, 
                fontsize=14, fontweight='bold', ha='center', va='center',
                color=color, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # 中心控制核心
    center_circle = Circle((center_x, center_y), inner_radius, 
                          facecolor='white', edgecolor='#1976D2', linewidth=3)
    ax.add_patch(center_circle)
    ax.text(center_x, center_y, '智能控制\n核心', 
            fontsize=12, fontweight='bold', ha='center', va='center',
            color='#1976D2')
    
    # =============== 第一层：智能感知层详细展开 ===============
    perception_box = FancyBboxPatch((12.5, 10.5), 7, 3.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['perception'], alpha=0.15,
                                   edgecolor=colors['perception'], linewidth=3)
    ax.add_patch(perception_box)
    ax.text(16, 13.7, '第一层：智能感知层', 
            fontsize=16, fontweight='bold', ha='center', va='center', 
            color=colors['perception'])
    
    perception_items = [
        ('多传感器融合\n• 位置传感器\n• 力/力矩传感器\n• 视觉传感器', 13.5, 12.8),
        ('状态估计\n• 扩展卡尔曼滤波\n• 不确定性量化\n• 噪声处理', 16, 12.8),
        ('异常检测\n• 统计过程控制\n• 阈值监控\n• 故障诊断', 18.5, 12.8),
        ('环境感知\n• 工作空间监控\n• 障碍物检测\n• 安全边界', 13.5, 11.5),
        ('数据预处理\n• 滤波降噪\n• 数据同步\n• 特征提取', 16, 11.5),
        ('感知融合\n• 多模态融合\n• 置信度评估\n• 信息集成', 18.5, 11.5)
    ]
    
    for text, x, y in perception_items:
        box = FancyBboxPatch((x-0.7, y-0.4), 1.4, 0.8, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['perception'], alpha=0.3,
                            edgecolor=colors['perception'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第二层：智能决策层详细展开 ===============
    decision_box = FancyBboxPatch((0.5, 10.5), 7, 3.5, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['decision'], alpha=0.15,
                                 edgecolor=colors['decision'], linewidth=3)
    ax.add_patch(decision_box)
    ax.text(4, 13.7, '第二层：智能决策层', 
            fontsize=16, fontweight='bold', ha='center', va='center', 
            color=colors['decision'])
    
    decision_items = [
        ('误差预测\n• 动态系统建模\n• 多步预测算法\n• 置信度估计', 1.5, 12.8),
        ('策略选择\n• 控制策略库\n• 自适应选择\n• 性能评估', 4, 12.8),
        ('权重调整\n• 多目标权重\n• 动态平衡\n• 优先级管理', 6.5, 12.8),
        ('约束处理\n• 安全约束\n• 性能约束\n• 物理约束', 1.5, 11.5),
        ('轨迹规划\n• B样条优化\n• 路径规划\n• 时间参数化', 4, 11.5),
        ('决策融合\n• 多准则决策\n• 风险评估\n• 决策优化', 6.5, 11.5)
    ]
    
    for text, x, y in decision_items:
        box = FancyBboxPatch((x-0.7, y-0.4), 1.4, 0.8, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['decision'], alpha=0.3,
                            edgecolor=colors['decision'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第三层：智能执行层详细展开 ===============
    execution_box = FancyBboxPatch((0.5, 2.5), 7, 3.5, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['execution'], alpha=0.15,
                                  edgecolor=colors['execution'], linewidth=3)
    ax.add_patch(execution_box)
    ax.text(4, 5.7, '第三层：智能执行层', 
            fontsize=16, fontweight='bold', ha='center', va='center', 
            color=colors['execution'])
    
    execution_items = [
        ('前馈控制\n• 逆动力学补偿\n• 轨迹前馈\n• 预测补偿', 1.5, 4.8),
        ('反馈控制\n• PID控制\n• 误差校正\n• 稳定性保证', 4, 4.8),
        ('预测控制\n• 模型预测控制\n• 约束优化\n• 滚动时域', 6.5, 4.8),
        ('智能融合\n• 多策略融合\n• 置信度加权\n• 自适应切换', 1.5, 3.5),
        ('实时执行\n• 1kHz控制频率\n• 硬实时保证\n• 延迟补偿', 4, 3.5),
        ('安全监控\n• 碰撞检测\n• 紧急停止\n• 故障恢复', 6.5, 3.5)
    ]
    
    for text, x, y in execution_items:
        box = FancyBboxPatch((x-0.7, y-0.4), 1.4, 0.8, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['execution'], alpha=0.3,
                            edgecolor=colors['execution'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 第四层：智能学习层详细展开 ===============
    learning_box = FancyBboxPatch((12.5, 2.5), 7, 3.5, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['learning'], alpha=0.15,
                                 edgecolor=colors['learning'], linewidth=3)
    ax.add_patch(learning_box)
    ax.text(16, 5.7, '第四层：智能学习层', 
            fontsize=16, fontweight='bold', ha='center', va='center', 
            color=colors['learning'])
    
    learning_items = [
        ('在线学习\n• 递归最小二乘\n• 参数自适应\n• 实时更新', 13.5, 4.8),
        ('神经网络\n• 误差补偿网络\n• 在线训练\n• 非线性学习', 16, 4.8),
        ('经验积累\n• 经验回放\n• 知识蒸馏\n• 模式记忆', 18.5, 4.8),
        ('性能评估\n• 性能指标监控\n• 趋势分析\n• 基准比较', 13.5, 3.5),
        ('模型更新\n• 动态模型调整\n• 参数优化\n• 结构进化', 16, 3.5),
        ('持续改进\n• 自适应优化\n• 智能调节\n• 长期学习', 18.5, 3.5)
    ]
    
    for text, x, y in learning_items:
        box = FancyBboxPatch((x-0.7, y-0.4), 1.4, 0.8, 
                            boxstyle="round,pad=0.05", 
                            facecolor=colors['learning'], alpha=0.3,
                            edgecolor=colors['learning'], linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # =============== 控制回路数据流连接 ===============
    # 主要数据流箭头
    main_flow_arrows = [
        # 感知 -> 决策
        (14.5, 10.5, 13, 9.5, colors['perception']),
        # 决策 -> 执行  
        (6.5, 10.5, 6.5, 6, colors['decision']),
        # 执行 -> 学习
        (7.5, 4, 12.5, 4, colors['execution']),
        # 学习 -> 感知 (反馈)
        (16, 6, 16, 10.5, colors['learning'])
    ]
    
    for x1, y1, x2, y2, color in main_flow_arrows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                               connectionstyle="arc3,rad=0.1", 
                               arrowstyle='->', 
                               mutation_scale=25, 
                               color=color, alpha=0.8, linewidth=4)
        ax.add_artist(arrow)
    
    # 中心到各层的连接
    center_connections = [
        # 中心到感知层
        (13, 9.5, colors['perception']),
        # 中心到决策层
        (7, 9.5, colors['decision']),
        # 中心到执行层
        (7, 6.5, colors['execution']),
        # 中心到学习层
        (13, 6.5, colors['learning'])
    ]
    
    for x, y, color in center_connections:
        arrow = FancyArrowPatch((center_x, center_y), (x, y),
                               arrowstyle='<->', 
                               mutation_scale=20, 
                               color=color, alpha=0.6, linewidth=3)
        ax.add_artist(arrow)
    
    # =============== 性能指标和技术特征 ===============
    # 左侧性能指标
    metrics_box = FancyBboxPatch((0.5, 7), 3, 2.5, 
                                boxstyle="round,pad=0.1", 
                                facecolor='#FFC107', alpha=0.2,
                                edgecolor='#FF8F00', linewidth=2)
    ax.add_patch(metrics_box)
    ax.text(2, 8.7, '关键性能指标', 
            fontsize=14, fontweight='bold', ha='center', va='center', 
            color='#FF8F00')
    
    metrics_text = """
• 跟踪精度: 0.070mm
• 响应时间: 8.5ms  
• 控制频率: 1kHz
• 系统延迟: <3ms
• 能效提升: 22%
• 鲁棒性: 0.900
"""
    ax.text(2, 7.8, metrics_text, ha='center', va='center', 
            fontsize=10, color='#F57C00', fontweight='bold')
    
    # 右侧技术特征
    features_box = FancyBboxPatch((16.5, 7), 3, 2.5, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor='#4CAF50', alpha=0.2,
                                 edgecolor='#2E7D32', linewidth=2)
    ax.add_patch(features_box)
    ax.text(18, 8.7, '技术创新特征', 
            fontsize=14, fontweight='bold', ha='center', va='center', 
            color='#2E7D32')
    
    features_text = """
• 四层智能架构
• 多目标B样条优化
• 预测误差补偿
• 自适应学习机制
• 实时性能保障
• 鲁棒控制设计
"""
    ax.text(18, 7.8, features_text, ha='center', va='center', 
            fontsize=10, color='#388E3C', fontweight='bold')
    
    # =============== 底部信息流程 ===============
    # 信息流程说明
    flow_box = FancyBboxPatch((2, 0.5), 16, 1.5, 
                             boxstyle="round,pad=0.1", 
                             facecolor='#E3F2FD', alpha=0.8,
                             edgecolor='#1976D2', linewidth=2)
    ax.add_patch(flow_box)
    
    flow_text = """
控制回路信息流：传感器数据 → 状态估计 → 误差预测 → 策略决策 → 轨迹优化 → 
控制执行 → 性能监控 → 在线学习 → 模型更新 → 参数优化 → 反馈调整
"""
    ax.text(10, 1.5, '信息流程', 
            fontsize=14, fontweight='bold', ha='center', va='center', 
            color='#1976D2')
    ax.text(10, 1, flow_text, ha='center', va='center', 
            fontsize=11, color='#0D47A1', style='italic')
    
    # 添加数据流速率标注
    data_rates = [
        ('1kHz', 14, 10, colors['perception']),
        ('1kHz', 7, 8, colors['decision']),
        ('1kHz', 7, 5, colors['execution']),
        ('100Hz', 15, 5, colors['learning'])
    ]
    
    for rate, x, y, color in data_rates:
        ax.text(x, y, rate, fontsize=12, fontweight='bold', 
                ha='center', va='center', color=color,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', 
                         edgecolor=color, linewidth=2))
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在生成感知-决策-执行-学习控制回路技术路线图...")
    
    # 生成图表
    fig = create_control_loop_roadmap()
    
    # 保存为超高清PNG
    output_file = "Intelligent_Control_Loop_Architecture.png"
    fig.savefig(output_file, dpi=400, bbox_inches='tight', 
                facecolor='white', edgecolor='none', 
                pad_inches=0.3, format='png')
    
    print(f"控制回路架构图已成功保存为: {output_file}")
    print("图像分辨率: 400 DPI (超高清)")
    print("图像格式: PNG")
    print("图像尺寸: 20x16 英寸")
    
    # 同时保存PDF版本
    pdf_file = "Intelligent_Control_Loop_Architecture.pdf"
    fig.savefig(pdf_file, bbox_inches='tight', 
                facecolor='white', edgecolor='none', 
                pad_inches=0.3, format='pdf')
    print(f"PDF版本已保存为: {pdf_file}")
    
    # 显示图表
    plt.show()
    
    return output_file

if __name__ == "__main__":
    main()