#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于参考图片风格的感知-决策-执行-学习控制回路技术路线图生成器
仿照学术论文图片的三栏式布局和模块化设计
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, Rectangle
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_academic_control_roadmap():
    """创建学术风格的控制回路技术路线图"""
    
    print("正在生成学术风格的感知-决策-执行-学习控制回路技术路线图...")
    
    # 创建大尺寸画布
    fig, ax = plt.subplots(figsize=(24, 16))
    ax.set_xlim(0, 24)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    # 定义专业颜色方案
    colors = {
        'perception': '#E3F2FD',      # 浅蓝色 - 感知层
        'decision': '#E8F5E8',        # 浅绿色 - 决策层  
        'execution': '#FFF3E0',       # 浅橙色 - 执行层
        'learning': '#F3E5F5',        # 浅紫色 - 学习层
        'framework': '#F5F5F5',       # 灰色 - 研究框架
        'application': '#E1F5FE',     # 青色 - 应用输出
        'border': '#424242',          # 深灰色 - 边框
        'arrow': '#1976D2',           # 蓝色 - 箭头
        'title': '#1565C0'           # 深蓝色 - 标题
    }
    
    # ===================================================================
    # 左栏：研究框架（Research Framework）
    # ===================================================================
    
    # 研究框架主标题
    ax.text(3, 15.2, '研究框架', fontsize=18, fontweight='bold', ha='center', va='center',
            color=colors['title'])
    
    # 研究背景框
    research_bg = FancyBboxPatch((0.5, 13), 5, 1.8, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['framework'], 
                                edgecolor=colors['border'], linewidth=2)
    ax.add_patch(research_bg)
    ax.text(3, 14.2, '基础科学', fontsize=14, fontweight='bold', ha='center', va='center')
    ax.text(1, 13.7, '• 机器人动力学建模', fontsize=10, ha='left', va='center')
    ax.text(1, 13.4, '• 多目标优化理论', fontsize=10, ha='left', va='center')
    ax.text(1, 13.1, '• 预测控制理论', fontsize=10, ha='left', va='center')
    
    # 分析问题框
    analysis_bg = FancyBboxPatch((0.5, 10.5), 5, 2, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['framework'], 
                                edgecolor=colors['border'], linewidth=2)
    ax.add_patch(analysis_bg)
    ax.text(3, 11.8, '分析问题', fontsize=14, fontweight='bold', ha='center', va='center')
    ax.text(1, 11.4, '• 轨迹跟踪精度限制', fontsize=10, ha='left', va='center')
    ax.text(1, 11.1, '• 多目标冲突优化', fontsize=10, ha='left', va='center')
    ax.text(1, 10.8, '• 实时性能要求', fontsize=10, ha='left', va='center')
    
    # 解决问题框  
    solution_bg = FancyBboxPatch((0.5, 8), 5, 2, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['framework'], 
                                edgecolor=colors['border'], linewidth=2)
    ax.add_patch(solution_bg)
    ax.text(3, 9.3, '解决问题', fontsize=14, fontweight='bold', ha='center', va='center')
    ax.text(1, 8.9, '• 智能集成控制框架', fontsize=10, ha='left', va='center')
    ax.text(1, 8.6, '• 预测误差补偿机制', fontsize=10, ha='left', va='center')
    ax.text(1, 8.3, '• 多层协同优化策略', fontsize=10, ha='left', va='center')
    
    # 研究机理框
    mechanism_bg = FancyBboxPatch((0.5, 5.5), 5, 2, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['framework'], 
                                 edgecolor=colors['border'], linewidth=2)
    ax.add_patch(mechanism_bg)
    ax.text(3, 6.8, '验证机理', fontsize=14, fontweight='bold', ha='center', va='center')
    ax.text(1, 6.4, '• 仿真验证平台', fontsize=10, ha='left', va='center')
    ax.text(1, 6.1, '• 实验验证系统', fontsize=10, ha='left', va='center')
    ax.text(1, 5.8, '• 性能指标评估', fontsize=10, ha='left', va='center')
    
    # ===================================================================
    # 中栏：核心技术模块（Core Technology Modules）
    # ===================================================================
    
    # 主标题
    ax.text(12, 15.2, '感知-决策-执行-学习智能控制回路', fontsize=20, fontweight='bold', 
            ha='center', va='center', color=colors['title'])
    
    # --------------------------------
    # 第一层虚线框：全链创新趋势、创新力驱使与国际化权威研究
    # --------------------------------
    
    # 创新趋势总框架（虚线）
    innovation_frame = Rectangle((7, 12.8), 10, 1.8, linewidth=2, edgecolor=colors['border'], 
                               facecolor='none', linestyle='--')
    ax.add_patch(innovation_frame)
    ax.text(12, 14.4, '全链创新趋势、创新力驱使与国际化权威研究', fontsize=12, fontweight='bold', 
            ha='center', va='center', color=colors['title'])
    
    # 创新趋势三个模块
    # 创新趋势
    trend_box = FancyBboxPatch((7.5, 13.2), 2.8, 0.8, 
                              boxstyle="round,pad=0.05", 
                              facecolor='#B3E5FC', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(trend_box)
    ax.text(8.9, 13.6, '创新趋势', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 创新力驱动
    drive_box = FancyBboxPatch((10.6, 13.2), 2.8, 0.8, 
                              boxstyle="round,pad=0.05", 
                              facecolor='#B3E5FC', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(drive_box)
    ax.text(12, 13.6, '创新力驱动', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 国际化权威
    authority_box = FancyBboxPatch((13.7, 13.2), 2.8, 0.8, 
                                  boxstyle="round,pad=0.05", 
                                  facecolor='#B3E5FC', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(authority_box)
    ax.text(15.1, 13.6, '国际化权威', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # --------------------------------  
    # 第二层虚线框：感知层、决策层、执行层、学习层
    # --------------------------------
    
    # 四层控制架构总框架（虚线）
    control_frame = Rectangle((7, 9.5), 10, 2.8, linewidth=2, edgecolor=colors['border'], 
                            facecolor='none', linestyle='--')
    ax.add_patch(control_frame)
    
    # 感知层
    perception_box = FancyBboxPatch((7.5, 11.5), 2.2, 0.6, 
                                   boxstyle="round,pad=0.05", 
                                   facecolor=colors['perception'], 
                                   edgecolor=colors['border'], linewidth=1)
    ax.add_patch(perception_box)
    ax.text(8.6, 11.8, '智能感知层', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 决策层
    decision_box = FancyBboxPatch((10.1, 11.5), 2.2, 0.6, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor=colors['decision'], 
                                 edgecolor=colors['border'], linewidth=1)
    ax.add_patch(decision_box)
    ax.text(11.2, 11.8, '智能决策层', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 执行层
    execution_box = FancyBboxPatch((12.7, 11.5), 2.2, 0.6, 
                                  boxstyle="round,pad=0.05", 
                                  facecolor=colors['execution'], 
                                  edgecolor=colors['border'], linewidth=1)
    ax.add_patch(execution_box)
    ax.text(13.8, 11.8, '智能执行层', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 学习层
    learning_box = FancyBboxPatch((15.3, 11.5), 2.2, 0.6, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor=colors['learning'], 
                                 edgecolor=colors['border'], linewidth=1)
    ax.add_patch(learning_box)
    ax.text(16.4, 11.8, '智能学习层', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # --------------------------------
    # 具体技术模块详细展开
    # --------------------------------
    
    # 感知层详细模块
    perception_details = [
        ('前沿学科', 7.8, 10.8),
        ('专利分布', 8.8, 10.8),
        ('人员交流', 7.8, 10.4),
        ('产业应用', 8.8, 10.4)
    ]
    
    for text, x, y in perception_details:
        detail_box = FancyBboxPatch((x-0.35, y-0.15), 0.7, 0.3, 
                                   boxstyle="round,pad=0.02", 
                                   facecolor='#FFE0B2', edgecolor=colors['border'], linewidth=1)
        ax.add_patch(detail_box)
        ax.text(x, y, text, fontsize=9, ha='center', va='center')
    
    # 决策层详细模块
    decision_details = [
        ('"科学技术产业"', 10.4, 10.8),
        ('原创价值体现', 11.4, 10.8),
        ('"技术效力"', 10.4, 10.4),
        ('激励价值体现', 11.4, 10.4)
    ]
    
    for text, x, y in decision_details:
        detail_box = FancyBboxPatch((x-0.35, y-0.15), 0.7, 0.3, 
                                   boxstyle="round,pad=0.02", 
                                   facecolor='#FFE0B2', edgecolor=colors['border'], linewidth=1)
        ax.add_patch(detail_box)
        ax.text(x, y, text, fontsize=9, ha='center', va='center')
    
    # 执行层详细模块
    execution_details = [
        ('地位指数', 13, 10.8),
        ('参与度指数', 14, 10.8),
        ('竞争优势与劣势', 13, 10.4),
        ('', 14, 10.4)  # 空白模块
    ]
    
    for text, x, y in execution_details:
        if text:  # 只绘制非空模块
            detail_box = FancyBboxPatch((x-0.35, y-0.15), 0.7, 0.3, 
                                       boxstyle="round,pad=0.02", 
                                       facecolor='#FFE0B2', edgecolor=colors['border'], linewidth=1)
            ax.add_patch(detail_box)
            ax.text(x, y, text, fontsize=9, ha='center', va='center')
    
    # --------------------------------
    # 第三层虚线框：产业链和政策框架
    # --------------------------------
    
    # 全球创新产业链框架（虚线）
    industry_frame = Rectangle((7, 6.5), 10, 2.5, linewidth=2, edgecolor=colors['border'], 
                             facecolor='none', linestyle='--')
    ax.add_patch(industry_frame)
    ax.text(12, 8.7, '全球创新产业链框架的创新、解释与风险防范研究', fontsize=12, fontweight='bold', 
            ha='center', va='center', color=colors['title'])
    
    # 产业链管理与解构
    industry_mgmt_box = FancyBboxPatch((7.5, 7.8), 4.5, 0.6, 
                                      boxstyle="round,pad=0.05", 
                                      facecolor='#C8E6C9', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(industry_mgmt_box)
    ax.text(9.75, 8.1, '产业链管理与解构', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 产业链创新风险防范优化
    innovation_risk_box = FancyBboxPatch((12.5, 7.8), 4, 0.6, 
                                        boxstyle="round,pad=0.05", 
                                        facecolor='#C8E6C9', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(innovation_risk_box)
    ax.text(14.5, 8.1, '产业链创新风险防范优化', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 详细子模块
    left_modules = [
        ('产业链关注关联', 8, 7.3),
        ('产业链结构构', 9, 7.3),
        ('产业链数据社', 10, 7.3),
        ('产业链竞争影响', 8, 6.9),
        ('', 9, 6.9),  # 空白
        ('', 10, 6.9)   # 空白
    ]
    
    right_modules = [
        ('上游原材料关系', 13, 7.3),
        ('加工工艺关系', 14.5, 7.3),
        ('设计软件对外依赖', 16, 7.3),
        ('加工设备自含蓄底', 13, 6.9),
        ('上下游组合需求', 14.5, 6.9),
        ('', 16, 6.9)  # 空白
    ]
    
    for text, x, y in left_modules + right_modules:
        if text:  # 只绘制非空模块
            detail_box = FancyBboxPatch((x-0.4, y-0.12), 0.8, 0.24, 
                                       boxstyle="round,pad=0.02", 
                                       facecolor='#FFCDD2', edgecolor=colors['border'], linewidth=1)
            ax.add_patch(detail_box)
            ax.text(x, y, text, fontsize=8, ha='center', va='center')
    
    # --------------------------------
    # 第四层虚线框：应用层
    # --------------------------------
    
    # 应用框架（虚线）
    application_frame = Rectangle((7, 3.5), 10, 2.5, linewidth=2, edgecolor=colors['border'], 
                                facecolor='none', linestyle='--')
    ax.add_patch(application_frame)
    ax.text(12, 5.7, '全球创新产业链框架的创新及基础性理论机制研究', fontsize=12, fontweight='bold', 
            ha='center', va='center', color=colors['title'])
    
    # 市场需求和分工边界拓展
    market_box = FancyBboxPatch((7.5, 4.8), 4, 0.6, 
                               boxstyle="round,pad=0.05", 
                               facecolor='#E1BEE7', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(market_box)
    ax.text(9.5, 5.1, '市场需求', fontsize=11, fontweight='bold', ha='center', va='center')
    
    division_box = FancyBboxPatch((12, 4.8), 4.5, 0.6, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor='#E1BEE7', edgecolor=colors['border'], linewidth=1)
    ax.add_patch(division_box)
    ax.text(14.25, 5.1, '分工边界拓展', fontsize=11, fontweight='bold', ha='center', va='center')
    
    # 底部模块
    ax.text(12, 4.2, '产业链体系发展', fontsize=11, fontweight='bold', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.1", facecolor='#E1BEE7', edgecolor=colors['border']))
    
    # ===================================================================
    # 右栏：创新理论发展与应用输出（Innovation Theory & Applications）
    # ===================================================================
    
    # 应用输出主标题
    ax.text(21, 15.2, '方法', fontsize=18, fontweight='bold', ha='center', va='center',
            color=colors['title'])
    
    # 理论创新研究进展框
    theory_bg = FancyBboxPatch((18.5, 12.5), 5, 2.3, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['application'], 
                              edgecolor=colors['border'], linewidth=2)
    ax.add_patch(theory_bg)
    ax.text(21, 14.4, '理论创新研究\n进展与影响', fontsize=14, fontweight='bold', 
            ha='center', va='center')
    ax.text(19, 13.7, '• B样条轨迹优化算法', fontsize=10, ha='left', va='center')
    ax.text(19, 13.4, '• 预测误差补偿框架', fontsize=10, ha='left', va='center')
    ax.text(19, 13.1, '• 智能融合控制策略', fontsize=10, ha='left', va='center')
    ax.text(19, 12.8, '• 在线学习与适应', fontsize=10, ha='left', va='center')
    
    # 系统级解决方案框
    system_bg = FancyBboxPatch((18.5, 9.5), 5, 2.5, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['application'], 
                              edgecolor=colors['border'], linewidth=2)
    ax.add_patch(system_bg)
    ax.text(21, 11.5, '系统级解决\n方案集成', fontsize=14, fontweight='bold', 
            ha='center', va='center')
    ax.text(19, 10.9, '• 实时控制系统(1kHz)', fontsize=10, ha='left', va='center')
    ax.text(19, 10.6, '• 多目标优化平台', fontsize=10, ha='left', va='center')
    ax.text(19, 10.3, '• 智能监控界面', fontsize=10, ha='left', va='center')
    ax.text(19, 10.0, '• 性能评估工具', fontsize=10, ha='left', va='center')
    ax.text(19, 9.7, '• 故障诊断系统', fontsize=10, ha='left', va='center')
    
    # 产业化应用框
    industry_app_bg = FancyBboxPatch((18.5, 6.5), 5, 2.5, 
                                    boxstyle="round,pad=0.1", 
                                    facecolor=colors['application'], 
                                    edgecolor=colors['border'], linewidth=2)
    ax.add_patch(industry_app_bg)
    ax.text(21, 8.5, '产业化应用\n验证平台', fontsize=14, fontweight='bold', 
            ha='center', va='center')
    ax.text(19, 7.9, '• 6DOF机器人书写系统', fontsize=10, ha='left', va='center')
    ax.text(19, 7.6, '• 工业装配线应用', fontsize=10, ha='left', va='center')
    ax.text(19, 7.3, '• 医疗手术机器人', fontsize=10, ha='left', va='center')
    ax.text(19, 7.0, '• 精密加工设备', fontsize=10, ha='left', va='center')
    ax.text(19, 6.7, '• 智能制造生产线', fontsize=10, ha='left', va='center')
    
    # ===================================================================
    # 绘制连接箭头（Flow Connections）
    # ===================================================================
    
    # 左栏内部垂直箭头
    for i in range(3):
        y_start = 12.8 - i*2.5
        y_end = y_start - 1.5
        arrow = patches.FancyArrowPatch((3, y_start), (3, y_end),
                                       arrowstyle='->', mutation_scale=20, 
                                       color=colors['arrow'], linewidth=2)
        ax.add_patch(arrow)
    
    # 左栏到中栏的连接箭头
    arrow1 = patches.FancyArrowPatch((5.5, 11), (7, 11.8),
                                    arrowstyle='->', mutation_scale=20, 
                                    color=colors['arrow'], linewidth=2)
    ax.add_patch(arrow1)
    
    # 中栏到右栏的连接箭头  
    arrow2 = patches.FancyArrowPatch((17, 11), (18.5, 11),
                                    arrowstyle='->', mutation_scale=20, 
                                    color=colors['arrow'], linewidth=2)
    ax.add_patch(arrow2)
    
    # 右栏内部垂直箭头
    for i in range(2):
        y_start = 12.3 - i*3
        y_end = y_start - 1.5
        arrow = patches.FancyArrowPatch((21, y_start), (21, y_end),
                                       arrowstyle='->', mutation_scale=20, 
                                       color=colors['arrow'], linewidth=2)
        ax.add_patch(arrow)
    
    # ===================================================================
    # 性能指标标注
    # ===================================================================
    
    # 性能提升指标
    performance_text = "关键性能指标:\n• 跟踪精度提升: 53%\n• 响应时间减少: 29%\n• 能耗效率改善: 22%"
    ax.text(12, 2.5, performance_text, fontsize=12, fontweight='bold', 
            ha='center', va='center', color=colors['title'],
            bbox=dict(boxstyle="round,pad=0.3", facecolor='#FFEB3B', alpha=0.3, 
                     edgecolor=colors['border'], linewidth=2))
    
    # 添加标题和副标题
    fig.suptitle('6DOF机器人智能书写系统：感知-决策-执行-学习控制回路技术路线图', 
                fontsize=24, fontweight='bold', y=0.95, color=colors['title'])
    
    ax.text(12, 0.8, '基于四层分层架构的智能集成控制框架 | 多目标B样条轨迹优化 | 预测误差补偿机制', 
            fontsize=14, ha='center', va='center', style='italic', color=colors['border'])
    
    # 设置图像属性
    plt.tight_layout()
    
    # 保存高质量图像
    output_filename = 'Academic_Control_Loop_Roadmap_Style'
    plt.savefig(f'{output_filename}.png', dpi=400, bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.2)
    plt.savefig(f'{output_filename}.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.2)
    
    plt.show()
    
    print(f"✅ 学术风格技术路线图已成功生成!")
    print(f"📁 PNG格式文件: {output_filename}.png")
    print(f"📁 PDF格式文件: {output_filename}.pdf")
    print(f"🎯 图像分辨率: 400 DPI (超高清)")
    print(f"📐 图像尺寸: 24x16 英寸")
    
    return fig, ax

if __name__ == "__main__":
    create_academic_control_roadmap()